<?php
// Error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('../login.php');
}

// Connect to database
try {
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception("Database connection failed");
    }
} catch (Exception $e) {
    die("Database Error: " . $e->getMessage());
}

// Get statistics for dashboard with error handling
$stats = [];
$error_message = '';

// Check if tables exist first
$tables_exist = true;
$required_tables = ['books', 'members', 'book_loans'];

try {
    foreach ($required_tables as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        if ($stmt->rowCount() == 0) {
            $tables_exist = false;
            break;
        }
    }
} catch (Exception $e) {
    $tables_exist = false;
}

if ($tables_exist) {
    try {
        // Total books
        $query = "SELECT COUNT(*) as total FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['total_books'] = $result['total'] ?? 0;

        // Available books
        $query = "SELECT SUM(COALESCE(available_quantity, 0)) as available FROM books";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['available_books'] = $result['available'] ?? 0;

        // Total members
        $query = "SELECT COUNT(*) as total FROM members";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['total_members'] = $result['total'] ?? 0;

        // Active loans (currently borrowed - not overdue yet)
        $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'borrowed' AND due_date >= CURDATE()";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['active_loans'] = $result['total'] ?? 0;

        // Overdue books (explicitly overdue OR borrowed past due date)
        $query = "SELECT COUNT(*) as total FROM book_loans WHERE status = 'overdue' OR (status = 'borrowed' AND due_date < CURDATE())";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch();
        $stats['overdue_books'] = $result['total'] ?? 0;

    } catch (Exception $e) {
        // Set default values if queries fail
        $stats = [
            'total_books' => 0,
            'available_books' => 0,
            'total_members' => 0,
            'active_loans' => 0,
            'overdue_books' => 0
        ];
        $error_message = "Error loading statistics: " . $e->getMessage();
    }
} else {
    // Tables don't exist - set default values
    $stats = [
        'total_books' => 0,
        'available_books' => 0,
        'total_members' => 0,
        'active_loans' => 0,
        'overdue_books' => 0
    ];
    $error_message = "Database tables not found. Please run the database setup.";
}

// Enhanced Member borrowing statistics with error handling
try {
    $member_stats = [];

    // Get basic member stats safely
    $stmt = $db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'borrowed'");
    $stmt->execute();
    $member_stats['members_with_active_loans'] = $stmt->fetch()['count'] ?? 0;

    $stmt = $db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'returned'");
    $stmt->execute();
    $member_stats['members_who_returned'] = $stmt->fetch()['count'] ?? 0;

    $stmt = $db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE status = 'overdue'");
    $stmt->execute();
    $member_stats['members_with_overdue'] = $stmt->fetch()['count'] ?? 0;

    $stmt = $db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans");
    $stmt->execute();
    $member_stats['members_who_borrowed'] = $stmt->fetch()['count'] ?? 0;

    $stmt = $db->prepare("SELECT COALESCE(SUM(fine), 0) as total FROM book_loans WHERE fine > 0");
    $stmt->execute();
    $member_stats['total_fines'] = $stmt->fetch()['total'] ?? 0;

    $stmt = $db->prepare("SELECT COUNT(DISTINCT member_id) as count FROM book_loans WHERE fine > 0");
    $stmt->execute();
    $member_stats['members_with_fines'] = $stmt->fetch()['count'] ?? 0;

    // Add missing loans_with_fines stat
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM book_loans WHERE fine > 0");
    $stmt->execute();
    $member_stats['loans_with_fines'] = $stmt->fetch()['count'] ?? 0;

} catch (Exception $e) {
    $member_stats = [
        'members_with_active_loans' => 0,
        'members_who_returned' => 0,
        'members_with_overdue' => 0,
        'members_who_borrowed' => 0,
        'total_fines' => 0,
        'members_with_fines' => 0,
        'loans_with_fines' => 0
    ];
}

// Simplified member analytics with error handling
$advanced_member_analytics = [];

try {
    // Simple activity levels - just get basic counts
    $advanced_member_analytics['activity_levels'] = [
        ['activity_level' => 'Active', 'member_count' => $member_stats['members_with_active_loans']],
        ['activity_level' => 'Returned', 'member_count' => $member_stats['members_who_returned']],
        ['activity_level' => 'Overdue', 'member_count' => $member_stats['members_with_overdue']]
    ];
} catch (Exception $e) {
    $advanced_member_analytics['activity_levels'] = [];
}

// Simplified top borrowers and recent activity
try {
    // Top borrowers - simplified query with email and additional stats
    $top_borrowers_query = "SELECT m.first_name, m.last_name, m.email,
                                   COUNT(bl.id) as total_loans,
                                   COUNT(CASE WHEN bl.status = 'borrowed' THEN 1 END) as active_loans,
                                   COUNT(CASE WHEN bl.status = 'overdue' THEN 1 END) as overdue_loans
                           FROM members m
                           LEFT JOIN book_loans bl ON m.id = bl.member_id
                           GROUP BY m.id, m.first_name, m.last_name, m.email
                           HAVING total_loans > 0
                           ORDER BY total_loans DESC
                           LIMIT 5";
    $stmt = $db->prepare($top_borrowers_query);
    $stmt->execute();
    $advanced_member_analytics['top_borrowers'] = $stmt->fetchAll();

    // Recent activity - simplified with due_date
    $recent_activity_query = "SELECT m.first_name, m.last_name, b.title, bl.issue_date, bl.due_date, bl.status
                             FROM book_loans bl
                             JOIN members m ON bl.member_id = m.id
                             JOIN books b ON bl.book_id = b.id
                             ORDER BY bl.issue_date DESC
                             LIMIT 5";
    $stmt = $db->prepare($recent_activity_query);
    $stmt->execute();
    $advanced_member_analytics['recent_activity'] = $stmt->fetchAll();

    // Simple risk analysis
    $advanced_member_analytics['risk_analysis'] = [
        'high_risk_members' => $member_stats['members_with_overdue'],
        'medium_risk_members' => 0,
        'high_fine_members' => $member_stats['members_with_fines'],
        'avg_overdue_per_member' => 0
    ];

} catch (Exception $e) {
    $advanced_member_analytics['top_borrowers'] = [];
    $advanced_member_analytics['recent_activity'] = [];
    $advanced_member_analytics['risk_analysis'] = [
        'high_risk_members' => 0,
        'medium_risk_members' => 0,
        'high_fine_members' => 0,
        'avg_overdue_per_member' => 0
    ];
}

// Calculate members who never borrowed (with safe defaults)
$stats['members_never_borrowed'] = max(0, ($stats['total_members'] ?? 0) - ($member_stats['members_who_borrowed'] ?? 0));
$stats['members_with_active_loans'] = $member_stats['members_with_active_loans'] ?? 0;
$stats['members_with_overdue'] = $member_stats['members_with_overdue'] ?? 0;
$stats['members_who_returned'] = $member_stats['members_who_returned'] ?? 0;
$stats['total_fines'] = $member_stats['total_fines'] ?? 0;
$stats['loans_with_fines'] = $member_stats['loans_with_fines'] ?? 0;
$stats['members_with_fines'] = $member_stats['members_with_fines'] ?? 0;

// Get notifications with error handling
$notifications = [];
$unread_count = 0;

try {
    // Check if notifications table exists
    $check_table = "SHOW TABLES LIKE 'notifications'";
    $stmt = $db->prepare($check_table);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $user_id = $_SESSION['user_id'] ?? null;
        $query = "SELECT * FROM notifications WHERE (user_id = :user_id OR user_id IS NULL) AND is_read = 0 ORDER BY created_at DESC LIMIT 5";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        $notifications = $stmt->fetchAll();

        // Count unread notifications
        if (function_exists('getUnreadNotificationsCount')) {
            $unread_count = getUnreadNotificationsCount($db, $user_id);
        } else {
            $count_query = "SELECT COUNT(*) as count FROM notifications WHERE (user_id = :user_id OR user_id IS NULL) AND is_read = 0";
            $count_stmt = $db->prepare($count_query);
            $count_stmt->bindParam(':user_id', $user_id);
            $count_stmt->execute();
            $result = $count_stmt->fetch();
            $unread_count = $result['count'] ?? 0;
        }
    }
} catch (Exception $e) {
    // Silently handle notification errors
    $notifications = [];
    $unread_count = 0;
}

// Get recent activity
$query = "SELECT al.*, u.username
          FROM activity_log al
          LEFT JOIN users u ON al.user_id = u.id
          ORDER BY al.timestamp DESC
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute();
$activities = $stmt->fetchAll();

// Get system information
$db_size = 0;
try {
    $query = "SELECT SUM(data_length + index_length) AS size FROM information_schema.TABLES WHERE table_schema = DATABASE()";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    $db_size = $result['size'] ?? 0;
} catch (Exception $e) {
    // Ignore errors
}

// Get disk space
$disk_total = formatFileSize(disk_total_space($_SERVER['DOCUMENT_ROOT']));
$disk_free = formatFileSize(disk_free_space($_SERVER['DOCUMENT_ROOT']));

// Get last backup time (placeholder - you would implement actual backup tracking)
$last_backup = null;
try {
    $query = "SELECT MAX(timestamp) AS last_backup FROM activity_log WHERE action = 'backup'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    $last_backup = $result['last_backup'] ?? null;
} catch (Exception $e) {
    // Ignore errors
}

// Log dashboard access
logActivity($db, 'view', 'Accessed admin dashboard');

// Recent books
$query = "SELECT * FROM books ORDER BY created_at DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_books = $stmt->fetchAll();

// Recent loans
$query = "SELECT bl.*, b.title as book_title, m.first_name, m.last_name
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          JOIN members m ON bl.member_id = m.id
          ORDER BY bl.issue_date DESC LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_loans = $stmt->fetchAll();

// Get book categories for chart
$query = "SELECT category, COUNT(*) as count FROM books GROUP BY category ORDER BY count DESC";
$stmt = $db->prepare($query);
$stmt->execute();
$categories = $stmt->fetchAll();

// Format category data for chart
$category_labels = [];
$category_data = [];
foreach ($categories as $category) {
    $category_labels[] = empty($category['category']) ? 'Uncategorized' : $category['category'];
    $category_data[] = $category['count'];
}

// Get loan data for the last 6 months
$query = "SELECT
            DATE_FORMAT(issue_date, '%b') as month,
            COUNT(*) as count
          FROM
            book_loans
          WHERE
            issue_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
          GROUP BY
            MONTH(issue_date), YEAR(issue_date)
          ORDER BY
            YEAR(issue_date), MONTH(issue_date)";
$stmt = $db->prepare($query);
$stmt->execute();
$loan_data = $stmt->fetchAll();

// Format loan data for chart
$loan_labels = [];
$loan_counts = [];
foreach ($loan_data as $data) {
    $loan_labels[] = $data['month'];
    $loan_counts[] = $data['count'];
}

// If we have less than 6 months of data, fill in the missing months
if (count($loan_labels) < 6) {
    $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    $current_month = date('n') - 1; // 0-based month index

    $loan_labels = [];
    $loan_counts = array_fill(0, 6, 0); // Initialize with zeros

    // Fill in the last 6 months
    for ($i = 5; $i >= 0; $i--) {
        $month_index = ($current_month - $i + 12) % 12; // Handle wrapping around to previous year
        $loan_labels[] = $months[$month_index];

        // Update counts for months we have data for
        foreach ($loan_data as $data) {
            if ($data['month'] === $months[$month_index]) {
                $loan_counts[5 - $i] = $data['count'];
                break;
            }
        }
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <?php
    $page_title = "Admin Dashboard";
    include_once __DIR__ . '/../includes/head.php';
    ?>
    <!-- Dashboard Fixes CSS -->
    <link rel="stylesheet" href="css/dashboard-fixes.css?v=<?php echo time(); ?>">
    <!-- Notifications CSS -->
    <link rel="stylesheet" href="../assets/css/notifications.css?v=<?php echo time(); ?>">
    <style>
        /* Force Light Theme - Override Dark Mode */
        body {
            font-size: .875rem;
            background-color: #f8f9fa !important;
            color: #212529 !important;
        }

        /* Force light theme on HTML element */
        html {
            background-color: #ffffff !important;
        }

        /* Override Bootstrap dark theme */
        [data-bs-theme="dark"] {
            --bs-body-bg: #f8f9fa !important;
            --bs-body-color: #212529 !important;
        }

        /* Force override any Bootstrap dark theme card colors */
        [data-bs-theme="dark"] .bg-primary,
        [data-bs-theme="light"] .bg-primary,
        .bg-primary {
            background-color: #007bff !important;
        }

        [data-bs-theme="dark"] .bg-success,
        [data-bs-theme="light"] .bg-success,
        .bg-success {
            background-color: #28a745 !important;
        }

        [data-bs-theme="dark"] .bg-info,
        [data-bs-theme="light"] .bg-info,
        .bg-info {
            background-color: #17a2b8 !important;
        }

        [data-bs-theme="dark"] .bg-warning,
        [data-bs-theme="light"] .bg-warning,
        .bg-warning {
            background-color: #ffc107 !important;
        }

        [data-bs-theme="dark"] .bg-danger,
        [data-bs-theme="light"] .bg-danger,
        .bg-danger {
            background-color: #dc3545 !important;
        }

        [data-bs-theme="dark"] .bg-secondary,
        [data-bs-theme="light"] .bg-secondary,
        .bg-secondary {
            background-color: #6c757d !important;
        }

        /* Force light sidebar */
        .sidebar {
            background-color: #f8f9fa !important;
            color: #212529 !important;
        }

        /* COMPLETE HEADER OVERRIDE - FORCE DARK THEME */
        header.navbar,
        .navbar,
        .navbar-dark,
        .bg-dark {
            background-color: #212529 !important;
            background: #212529 !important;
            border-bottom: 1px solid #dee2e6 !important;
        }

        /* Remove ALL blue backgrounds from header */
        header .bg-primary,
        .navbar .bg-primary {
            background-color: #212529 !important;
            background: #212529 !important;
        }

        /* Force all header text to be white and override style.css */
        .navbar-brand,
        .navbar .nav-link,
        .navbar .text-white {
            color: #ffffff !important;
            background-color: transparent !important;
            box-shadow: none !important;
        }

        /* Override style.css navbar-brand background */
        .navbar-brand {
            background-color: transparent !important;
            background: transparent !important;
        }

        /* FINAL OVERRIDE - Force entire header area to be dark */
        header,
        header *,
        .navbar,
        .navbar * {
            background-color: #212529 !important;
        }

        /* But keep the brand area transparent */
        .navbar-brand {
            background-color: transparent !important;
        }

        /* Force light cards - but preserve colored stat cards */
        .card:not(.stats-card) {
            background-color: #ffffff !important;
            color: #212529 !important;
            border: 1px solid #dee2e6 !important;
        }

        /* Restore colorful stats cards - Force override all dark mode */
        .card.stats-card.bg-primary,
        .stats-card.bg-primary,
        .bg-primary.stats-card {
            background: linear-gradient(45deg, #007bff, #0056b3) !important;
            background-color: #007bff !important;
            color: white !important;
            border-color: #007bff !important;
        }

        .card.stats-card.bg-success,
        .stats-card.bg-success,
        .bg-success.stats-card {
            background: linear-gradient(45deg, #28a745, #1e7e34) !important;
            background-color: #28a745 !important;
            color: white !important;
            border-color: #28a745 !important;
        }

        .card.stats-card.bg-info,
        .stats-card.bg-info,
        .bg-info.stats-card {
            background: linear-gradient(45deg, #17a2b8, #117a8b) !important;
            background-color: #17a2b8 !important;
            color: white !important;
            border-color: #17a2b8 !important;
        }

        .card.stats-card.bg-warning,
        .stats-card.bg-warning,
        .bg-warning.stats-card {
            background: linear-gradient(45deg, #ffc107, #e0a800) !important;
            background-color: #ffc107 !important;
            color: #212529 !important;
            border-color: #ffc107 !important;
        }

        .stats-card.bg-warning .text-white-50 {
            color: rgba(33, 37, 41, 0.7) !important;
        }

        .stats-card.bg-warning .text-white {
            color: #212529 !important;
        }

        .card.stats-card.bg-danger,
        .stats-card.bg-danger,
        .bg-danger.stats-card {
            background: linear-gradient(45deg, #dc3545, #c82333) !important;
            background-color: #dc3545 !important;
            color: white !important;
            border-color: #dc3545 !important;
        }

        .card.stats-card.bg-secondary,
        .stats-card.bg-secondary,
        .bg-secondary.stats-card {
            background: linear-gradient(45deg, #6c757d, #5a6268) !important;
            background-color: #6c757d !important;
            color: white !important;
            border-color: #6c757d !important;
        }

        /* Ensure text colors in stat cards */
        .stats-card .text-white-50 {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        .stats-card .text-white {
            color: white !important;
        }

        /* Fix card footers for colored cards */
        .stats-card .card-footer {
            background: rgba(0, 0, 0, 0.1) !important;
            border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        }

        .stats-card .card-footer a {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .stats-card.bg-warning .card-footer a {
            color: rgba(33, 37, 41, 0.8) !important;
        }

        /* Make notification bell and welcome text more visible */
        .navbar .bell-icon {
            background-color: rgba(255, 255, 255, 0.1) !important;
            border-radius: 8px !important;
            padding: 8px !important;
            transition: all 0.3s ease !important;
        }

        .navbar .bell-icon:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
            transform: scale(1.1) !important;
        }

        .navbar .bell-icon i {
            font-size: 1.2rem !important;
            color: #ffffff !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
        }

        /* Make welcome text more prominent */
        .navbar .nav-link.text-white,
        .navbar span.nav-link.text-white {
            background-color: rgba(255, 255, 255, 0.15) !important;
            border-radius: 8px !important;
            padding: 10px 15px !important;
            font-weight: 700 !important;
            font-size: 1rem !important;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            color: #ffffff !important;
            margin-right: 10px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
        }

        .navbar span.nav-link.text-white:hover {
            background-color: rgba(255, 255, 255, 0.25) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        }

        /* Notification badge more visible */
        .notification-badge {
            font-size: 0.75rem !important;
            font-weight: bold !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
            border: 2px solid white !important;
        }

        /* Animations for enhanced visibility */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes fadeInRight {
            0% {
                opacity: 0;
                transform: translateX(20px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced navbar styling */
        .navbar-dark {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
        }

        /* Force light main content area */
        main {
            background-color: #f8f9fa !important;
            color: #212529 !important;
        }

        /* Override any dark theme classes */
        .bg-dark {
            background-color: #f8f9fa !important;
        }

        .text-light {
            color: #212529 !important;
        }

        .feather {
            width: 16px;
            height: 16px;
            vertical-align: text-bottom;
        }

        /* Static Notifications Container */
        .static-notifications-container {
            max-height: 500px !important;
            overflow-y: auto !important;
            position: fixed !important;
            top: 70px !important;
            right: 20px !important;
            z-index: 9999 !important;
            width: 400px !important;
            background-color: #ffffff !important;
            border-radius: 15px !important;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4) !important;
            border: 3px solid #007bff !important;
            display: none !important; /* Hidden by default */
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        .static-notifications-container.show {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }

        .notifications-header {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
            color: white !important;
            padding: 15px !important;
            border-radius: 13px 13px 0 0 !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        .notifications-body {
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
        }

        .notification-item {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s ease;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-item.unread {
            background-color: #e3f2fd;
            border-left: 4px solid #007bff;
        }

        /* Responsive adjustments for notifications */
        @media (max-width: 767.98px) {
            .static-notifications-container {
                width: 90%;
                right: 5%;
                left: 5%;
            }
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }

        @media (max-width: 767.98px) {
            .sidebar {
                top: 5rem;
            }
        }

        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .sidebar .nav-link {
            font-weight: 500;
            color: #333;
            padding: .75rem 1rem;
        }

        .sidebar .nav-link.active {
            color: #2470dc;
        }

        .sidebar .nav-link:hover {
            color: #007bff;
        }

        .sidebar-heading {
            font-size: .75rem;
            text-transform: uppercase;
        }

        /* Navbar */
        .navbar-brand {
            padding-top: .75rem;
            padding-bottom: .75rem;
            font-size: 1rem;
            background-color: rgba(0, 0, 0, .25);
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
        }

        .navbar .navbar-toggler {
            top: .25rem;
            right: 1rem;
        }

        /* Dashboard specific styles */
        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 10px;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .card-footer {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
        }

        .card-footer a {
            color: rgba(255, 255, 255, 0.8) !important;
            text-decoration: none !important;
            font-weight: 500;
            transition: all 0.2s ease;
            pointer-events: auto !important;
            z-index: 10;
            position: relative;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
        }

        .card-footer a:hover {
            color: white !important;
            text-decoration: none !important;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        .view-details-link {
            cursor: pointer !important;
        }

        .stats-card .card-footer {
            cursor: default;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .cover-preview {
            display: block;
            transition: transform 0.2s;
        }

        .cover-preview:hover {
            transform: scale(1.1);
        }

        .img-thumbnail {
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        /* Dark mode form control */
        .form-control-dark {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .form-control-dark:focus {
            border-color: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
            color: #fff;
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
            .btn-toolbar {
                margin-top: 1rem;
                justify-content: center;
                width: 100%;
            }
        }

        /* Custom 5-column layout for member stats */
        @media (min-width: 992px) {
            .col-lg-2-4 {
                flex: 0 0 20%;
                max-width: 20%;
            }
        }

        /* Enhanced Member Statistics Styling */
        .bg-gradient-primary {
            background: linear-gradient(45deg, #007bff, #0056b3) !important;
        }

        .stats-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .activity-levels .progress {
            border-radius: 10px;
            background-color: rgba(0, 0, 0, 0.1);
        }

        .activity-levels .progress-bar {
            border-radius: 10px;
            transition: width 0.6s ease;
        }

        .list-group-item {
            border: none;
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .list-group-item:last-child {
            border-bottom: none;
        }

        .card-header {
            border-bottom: 2px solid rgba(0, 0, 0, 0.05);
            font-weight: 600;
        }

        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
        }

        /* Animation for statistics cards */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-card {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Responsive enhancements */
        @media (max-width: 768px) {
            .stats-card {
                margin-bottom: 1rem;
            }

            .card-body {
                padding: 1rem;
            }

            .fs-1 {
                font-size: 2rem !important;
            }
        }

        /* Spinning animation for refresh button */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        /* Fix for sign out button and navbar elements */
        .navbar-nav .nav-item {
            z-index: 1060;
            position: relative;
        }

        .navbar-nav .btn {
            z-index: 1060 !important;
            position: relative !important;
            pointer-events: auto !important;
        }

        /* Ensure notifications don't overlap important buttons */
        .static-notifications-container {
            z-index: 1040 !important;
        }

        /* Fix for error alerts */
        .alert {
            z-index: 1030;
            position: relative;
        }
    </style>
</head>
<body>
    <!-- Force Light Theme Script -->
    <script>
        // Force disable dark mode immediately
        document.documentElement.removeAttribute('data-bs-theme');
        document.documentElement.setAttribute('data-bs-theme', 'light');
        document.body.style.backgroundColor = '#f8f9fa';
        document.body.style.color = '#212529';

        // Remove any dark mode classes
        document.documentElement.classList.remove('dark-mode', 'dark-theme');
        document.body.classList.remove('dark-mode', 'dark-theme');

        // Override localStorage dark mode setting
        localStorage.setItem('theme', 'light');
        localStorage.removeItem('darkMode');

        // Force card colors after DOM loads
        document.addEventListener('DOMContentLoaded', function() {
            // Force colors on stat cards
            const colorMap = {
                'bg-primary': '#007bff',
                'bg-success': '#28a745',
                'bg-info': '#17a2b8',
                'bg-warning': '#ffc107',
                'bg-danger': '#dc3545',
                'bg-secondary': '#6c757d'
            };

            Object.keys(colorMap).forEach(className => {
                const cards = document.querySelectorAll(`.stats-card.${className}`);
                cards.forEach(card => {
                    card.style.backgroundColor = colorMap[className];
                    card.style.background = `linear-gradient(45deg, ${colorMap[className]}, ${colorMap[className]}dd)`;
                    card.style.color = className === 'bg-warning' ? '#212529' : 'white';
                    card.style.borderColor = colorMap[className];
                });
            });

            console.log('Card colors forced via JavaScript');

            // Enhanced notification bell functionality
            console.log('Setting up notification bell...');

            // Get elements
            const bellButton = document.getElementById('notificationBell');
            const notificationsContainer = document.getElementById('staticNotifications');
            const welcomeText = document.querySelector('.navbar span.nav-link.text-white');

            console.log('Bell button found:', !!bellButton);
            console.log('Notifications container found:', !!notificationsContainer);

            if (bellButton && notificationsContainer) {
                // Initialize notification container as hidden
                notificationsContainer.style.display = 'none';

                // Add visual enhancements to bell
                bellButton.style.boxShadow = '0 0 15px rgba(255, 255, 255, 0.3)';
                bellButton.style.transition = 'all 0.3s ease';

                // Add pulsing effect if there are notifications
                const notificationBadge = bellButton.querySelector('.notification-badge');
                if (notificationBadge) {
                    bellButton.style.animation = 'pulse 2s infinite';
                }

                // Enhanced click handler with multiple event types
                function toggleNotifications(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('Bell clicked!');

                    // Check current state and toggle
                    const isVisible = notificationsContainer.style.display === 'block';

                    if (isVisible) {
                        notificationsContainer.style.display = 'none';
                        notificationsContainer.classList.remove('show');
                        console.log('Notifications hidden');
                    } else {
                        notificationsContainer.style.display = 'block';
                        notificationsContainer.style.opacity = '1';
                        notificationsContainer.style.transform = 'translateY(0)';
                        notificationsContainer.classList.add('show');
                        console.log('Notifications shown');
                    }
                }

                // Add multiple event listeners for better compatibility
                bellButton.addEventListener('click', toggleNotifications);
                bellButton.addEventListener('touchstart', toggleNotifications);
                bellButton.onclick = toggleNotifications;

                // Add hover effect
                bellButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                    this.style.opacity = '0.8';
                });

                bellButton.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.opacity = '1';
                });

                console.log('Notification bell setup complete!');

                // Add a test button for debugging
                const testButton = document.createElement('button');
                testButton.innerHTML = 'Test Bell';
                testButton.style.cssText = 'position: fixed; top: 10px; left: 10px; z-index: 9999; background: red; color: white; padding: 5px; border: none; border-radius: 3px;';
                testButton.onclick = function() {
                    console.log('Test button clicked - triggering bell');
                    toggleNotifications(new Event('click'));
                };
                document.body.appendChild(testButton);

            } else {
                console.error('Could not find bell button or notifications container');
                console.log('Available elements:', {
                    bellButton: document.getElementById('notificationBell'),
                    notificationsContainer: document.getElementById('staticNotifications')
                });

                // Add debugging info
                console.log('All elements with notificationBell ID:', document.querySelectorAll('#notificationBell'));
                console.log('All elements with staticNotifications ID:', document.querySelectorAll('#staticNotifications'));
                console.log('All elements with bell-icon class:', document.querySelectorAll('.bell-icon'));
            }

            if (welcomeText) {
                // Make welcome text more prominent
                welcomeText.style.animation = 'fadeInRight 0.8s ease-out';
                welcomeText.style.textTransform = 'uppercase';
                welcomeText.style.letterSpacing = '1px';
            }

            // Setup notification controls
            if (notificationsContainer) {
                // Close button
                const closeBtn = document.getElementById('closeNotifications');
                if (closeBtn) {
                    closeBtn.onclick = function() {
                        notificationsContainer.style.display = 'none';
                        console.log('Notifications closed via close button');
                    };
                }

                // Refresh button
                const refreshBtn = document.getElementById('refreshNotifications');
                if (refreshBtn) {
                    refreshBtn.onclick = function() {
                        console.log('Refresh clicked');
                        this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
                        setTimeout(() => {
                            this.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                        }, 1000);
                    };
                }

                // Click outside to close
                document.onclick = function(e) {
                    if (notificationsContainer && bellButton) {
                        if (!notificationsContainer.contains(e.target) && !bellButton.contains(e.target)) {
                            if (notificationsContainer.style.display === 'block') {
                                notificationsContainer.style.display = 'none';
                                console.log('Notifications closed by clicking outside');
                            }
                        }
                    }
                };
            }
        });

        console.log('Light theme forced');

        // IMMEDIATE HEADER FIX - Force dark theme
        (function() {
            function forceHeaderDark() {
                const header = document.querySelector('header.navbar');
                const navbar = document.querySelector('.navbar');

                if (header) {
                    header.style.setProperty('background-color', '#212529', 'important');
                    header.style.setProperty('background', '#212529', 'important');
                    header.classList.remove('bg-primary', 'bg-blue');
                    header.classList.add('bg-dark');
                    console.log('Header forced to dark theme');
                }

                if (navbar) {
                    navbar.style.setProperty('background-color', '#212529', 'important');
                    navbar.style.setProperty('background', '#212529', 'important');
                    console.log('Navbar forced to dark theme');
                }
            }

            // Run immediately
            forceHeaderDark();

            // Run when DOM is ready
            document.addEventListener('DOMContentLoaded', forceHeaderDark);

            // Run after a short delay to override any other scripts
            setTimeout(forceHeaderDark, 100);
        })();
    </script>

    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow" style="background-color: #212529 !important; background: #212529 !important;">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">Library MS Admin</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="w-100">
            <form class="d-none d-md-flex w-50 mx-auto" action="../search.php" method="get">
                <input class="form-control form-control-dark" type="text" name="q" placeholder="Search books, members..." aria-label="Search" required>
                <button class="btn btn-outline-light ms-2" type="submit"><i class="bi bi-search"></i></button>
            </form>
        </div>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap d-flex align-items-center">
                <!-- Enhanced Notifications Panel -->
                <div class="nav-item me-2">
                    <button class="nav-link position-relative border-0 bg-transparent notification-bell-btn"
                            type="button"
                            id="notificationBell"
                            aria-label="Notifications<?php echo $unread_count > 0 ? " ($unread_count unread)" : ''; ?>"
                            aria-expanded="false"
                            aria-haspopup="true"
                            title="Click to view notifications"
                            style="cursor: pointer; padding: 8px 12px; border-radius: 6px; transition: all 0.3s ease;">
                        <span class="bell-icon">
                            <i class="bi bi-bell fs-5 text-white" style="filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));"></i>
                        </span>
                        <?php if ($unread_count > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge pulse-animation"
                                  style="font-size: 0.7rem; min-width: 18px; height: 18px; line-height: 18px;"
                                  aria-label="<?php echo $unread_count; ?> unread notifications">
                                <?php echo $unread_count > 9 ? '9+' : $unread_count; ?>
                            </span>
                        <?php endif; ?>
                    </button>
                </div>

                <!-- Static Notifications Container -->
                <div class="static-notifications-container" id="staticNotifications">
                    <div class="notifications-header">
                        <div class="d-flex align-items-center">
                            <h6 class="mb-0">Notifications</h6>
                            <?php if ($unread_count > 0): ?>
                                <span class="badge bg-danger ms-2 notification-counter"><?php echo $unread_count; ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="d-flex align-items-center">
                            <?php if ($unread_count > 0): ?>
                                <a href="../notifications/mark_all_read.php" class="mark-all-read me-2">Mark all as read</a>
                            <?php endif; ?>
                            <button type="button" class="btn btn-sm btn-outline-light me-1" id="refreshNotifications" title="Refresh">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light me-1" id="minimizeNotifications" title="Minimize">
                                <i class="bi bi-dash"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-light" id="closeNotifications" title="Close">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                    </div>

                    <div class="notifications-body">
                        <?php if (count($notifications) > 0): ?>
                            <?php foreach ($notifications as $notification): ?>
                                <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?> type-<?php echo h($notification['type']); ?>" data-notification-type="<?php echo h($notification['type']); ?>" data-notification-id="<?php echo h($notification['id']); ?>">
                                    <div class="d-flex">
                                        <div class="notification-icon bg-<?php echo h($notification['type']); ?>">
                                            <?php if ($notification['type'] === 'warning'): ?>
                                                <i class="bi bi-exclamation-triangle"></i>
                                            <?php elseif ($notification['type'] === 'danger'): ?>
                                                <i class="bi bi-exclamation-circle"></i>
                                            <?php elseif ($notification['type'] === 'success'): ?>
                                                <i class="bi bi-check-circle"></i>
                                            <?php else: ?>
                                                <i class="bi bi-info-circle"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="notification-content">
                                            <p class="notification-message"><?php echo h($notification['message']); ?></p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="notification-time"><?php echo timeAgo($notification['created_at']); ?></span>
                                                <?php if (!$notification['is_read']): ?>
                                                    <button class="btn btn-sm btn-link p-0 mark-read-btn" data-id="<?php echo h($notification['id']); ?>">Mark as read</button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <!-- Sample notifications for testing -->
                            <div class="notification-item unread type-info" data-notification-type="info">
                                <div class="d-flex">
                                    <div class="notification-icon bg-info">
                                        <i class="bi bi-info-circle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-message">Welcome to the Library Management System! Click the bell to see notifications.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="notification-time">Just now</span>
                                            <button class="btn btn-sm btn-link p-0 mark-read-btn">Mark as read</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="notification-item type-success" data-notification-type="success">
                                <div class="d-flex">
                                    <div class="notification-icon bg-success">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-message">System is running smoothly. All services are operational.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="notification-time">5 minutes ago</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="notification-item type-warning" data-notification-type="warning">
                                <div class="d-flex">
                                    <div class="notification-icon bg-warning">
                                        <i class="bi bi-exclamation-triangle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-message">Some books are overdue. Please check the overdue books report.</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="notification-time">1 hour ago</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="notifications-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="../notifications/index.php">View all notifications</a>
                            <span class="text-muted notification-count-display"><?php echo count($notifications); ?> total</span>
                        </div>
                    </div>
                </div>

                <span class="nav-link px-3 text-white">Welcome, Admin</span>
                <a class="btn btn-danger btn-sm mx-2" href="../logout.php">
                    <i class="bi bi-box-arrow-right me-1"></i>Sign out
                </a>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('books/index.php'); ?>">
                                <i class="bi bi-book me-2"></i>
                                Books
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('members/index.php'); ?>">
                                <i class="bi bi-people me-2"></i>
                                Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('loans/index.php'); ?>">
                                <i class="bi bi-journal-arrow-up me-2"></i>
                                Book Loans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo url('reports/index.php'); ?>">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>
                                Reports
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Administration</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="bi bi-person-gear me-2"></i>
                                Manage Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="bi bi-gear me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="user_access.php">
                                <i class="bi bi-link me-2"></i>
                                User Access Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../troubleshoot.php">
                                <i class="bi bi-tools me-2"></i>
                                Troubleshooting
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>Advanced Features</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="notifications.php">
                                <i class="bi bi-bell me-2"></i>
                                Notification Center
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="system_health.php">
                                <i class="bi bi-heart-pulse me-2"></i>
                                System Health
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="financial_management.php">
                                <i class="bi bi-currency-dollar me-2"></i>
                                Financial Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="activity_logs.php">
                                <i class="bi bi-activity me-2"></i>
                                Activity Logs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="quick_actions.php">
                                <i class="bi bi-lightning me-2"></i>
                                Quick Actions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="test_notifications.php">
                                <i class="bi bi-bell-fill me-2"></i>
                                Test Notifications
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <?php if (isset($error_message) && !empty($error_message)): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert" style="z-index: 1030;">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Notice:</strong> <?php echo h($error_message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Admin Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo url('books/add.php'); ?>" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-plus-circle me-1"></i> Add Book
                            </a>
                            <a href="<?php echo url('members/add.php'); ?>" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-person-plus me-1"></i> Add Member
                            </a>
                            <a href="<?php echo url('loans/issue.php'); ?>" class="btn btn-sm btn-outline-info">
                                <i class="bi bi-journal-plus me-1"></i> Issue Book
                            </a>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-file-earmark-text me-1"></i> Reports
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../reports/books.php">Books Report</a></li>
                            <li><a class="dropdown-item" href="../reports/members.php">Members Report</a></li>
                            <li><a class="dropdown-item" href="../reports/loans.php">Loans Report</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../reports/overdue_books.php">Overdue Books Report</a></li>
                        </ul>
                        <div class="btn-group ms-2">
                            <button type="button" class="btn btn-sm btn-outline-warning dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-tools me-1"></i> Diagnostics
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="../database_status.php" target="_blank">
                                    <i class="bi bi-database me-2"></i>Database Status
                                </a></li>
                                <li><a class="dropdown-item" href="ajax/test_database_clean.php" target="_blank">
                                    <i class="bi bi-bug me-2"></i>Advanced Test
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../troubleshoot.php" target="_blank">
                                    <i class="bi bi-question-circle me-2"></i>Troubleshooting Guide
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['message'])): ?>
                    <div class="alert alert-<?php echo h($_SESSION['message_type']); ?> alert-dismissible fade show" role="alert">
                        <?php echo h($_SESSION['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <!-- Stats Cards -->
                <div class="row">
                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-primary stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Book Titles</h6>
                                        <h3 class="mb-0" data-stat="total_books"><?php echo h($stats['total_books']); ?></h3>
                                        <small class="text-white-50">Unique books</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-book fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('books/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-success stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Available Copies</h6>
                                        <h3 class="mb-0" data-stat="available_books"><?php echo h($stats['available_books']); ?></h3>
                                        <small class="text-white-50">Ready to borrow</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-bookshelf fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('books/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-info stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Total Members</h6>
                                        <h3 class="mb-0" data-stat="total_members"><?php echo h($stats['total_members']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('members/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-warning stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Active Loans</h6>
                                        <h3 class="mb-0" data-stat="active_loans"><?php echo h($stats['active_loans']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-journal-arrow-up fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('loans/index.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 col-xl-3 mb-4">
                        <div class="card text-white bg-danger stats-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-white-50">Overdue Books</h6>
                                        <h3 class="mb-0" data-stat="overdue_books"><?php echo h($stats['overdue_books']); ?></h3>
                                    </div>
                                    <div>
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex align-items-center justify-content-between">
                                <a href="<?php echo url('loans/overdue.php'); ?>" class="text-white-50 text-decoration-none view-details-link">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <i class="bi bi-chevron-right text-white-50"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Member Borrowing Statistics -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-gradient-primary text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="bi bi-people me-2"></i>Enhanced Member Analytics Dashboard</h5>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="collapse" data-bs-target="#memberStatsCollapse" aria-expanded="true" aria-controls="memberStatsCollapse">
                                        <i class="bi bi-chevron-down"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="collapse show" id="memberStatsCollapse">
                                <div class="card-body">
                                    <!-- Primary Statistics Row -->
                                    <div class="row g-3 mb-4">
                                        <div class="col-lg-2-4 col-md-4 col-sm-6">
                                            <div class="card bg-primary text-white h-100 stats-card">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-people fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo h($stats['members_with_active_loans']); ?></h4>
                                                    <p class="mb-0 small">Currently Borrowing</p>
                                                    <small class="opacity-75">
                                                        <?php echo round(($stats['members_with_active_loans'] / max(1, $stats['total_members'])) * 100, 1); ?>% of total
                                                    </small>
                                                </div>
                                                <div class="card-footer bg-primary border-0">
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar bg-white" style="width: <?php echo round(($stats['members_with_active_loans'] / max(1, $stats['total_members'])) * 100, 1); ?>%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2-4 col-md-4 col-sm-6">
                                            <div class="card bg-success text-white h-100 stats-card">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-check-circle fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo h($stats['members_who_returned']); ?></h4>
                                                    <p class="mb-0 small">Returned Books</p>
                                                    <small class="opacity-75">
                                                        <?php echo round(($stats['members_who_returned'] / $stats['total_members']) * 100, 1); ?>% of total
                                                    </small>
                                                </div>
                                                <div class="card-footer bg-success border-0">
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar bg-white" style="width: <?php echo round(($stats['members_who_returned'] / $stats['total_members']) * 100, 1); ?>%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2-4 col-md-4 col-sm-6">
                                            <div class="card bg-danger text-white h-100 stats-card">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-exclamation-triangle fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo h($stats['members_with_overdue']); ?></h4>
                                                    <p class="mb-0 small">With Overdue Books</p>
                                                    <small class="opacity-75">
                                                        <?php echo round(($stats['members_with_overdue'] / $stats['total_members']) * 100, 1); ?>% of total
                                                    </small>
                                                </div>
                                                <div class="card-footer bg-danger border-0">
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar bg-white" style="width: <?php echo round(($stats['members_with_overdue'] / $stats['total_members']) * 100, 1); ?>%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2-4 col-md-4 col-sm-6">
                                            <div class="card bg-warning text-white h-100 stats-card">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-currency-dollar fs-1 mb-2"></i>
                                                    <h4 class="mb-1">$<?php echo number_format($stats['total_fines'], 2); ?></h4>
                                                    <p class="mb-0 small">Total Fines</p>
                                                    <small class="opacity-75">
                                                        <?php echo h($stats['members_with_fines']); ?> members owe fines
                                                    </small>
                                                </div>
                                                <div class="card-footer bg-warning border-0">
                                                    <small class="opacity-75">Avg: $<?php echo number_format($stats['total_fines'] / max($stats['members_with_fines'], 1), 2); ?> per member</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2-4 col-md-4 col-sm-6">
                                            <div class="card bg-secondary text-white h-100 stats-card">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-person-x fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo h($stats['members_never_borrowed']); ?></h4>
                                                    <p class="mb-0 small">Never Borrowed</p>
                                                    <small class="opacity-75">
                                                        <?php echo round(($stats['members_never_borrowed'] / $stats['total_members']) * 100, 1); ?>% of total
                                                    </small>
                                                </div>
                                                <div class="card-footer bg-secondary border-0">
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar bg-white" style="width: <?php echo round(($stats['members_never_borrowed'] / $stats['total_members']) * 100, 1); ?>%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Advanced Analytics Row -->
                                    <div class="row g-3 mb-4">
                                        <div class="col-md-3">
                                            <div class="card bg-info text-white h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-calendar-plus fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo h($member_stats['new_members_this_month'] ?? 0); ?></h4>
                                                    <p class="mb-0 small">New This Month</p>
                                                    <small class="opacity-75"><?php echo h($member_stats['new_members_this_week'] ?? 0); ?> this week</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-purple text-white h-100" style="background: linear-gradient(45deg, #6f42c1, #8e44ad);">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-graph-up fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo number_format($member_stats['avg_loans_per_member'] ?? 0, 1); ?></h4>
                                                    <p class="mb-0 small">Avg Loans/Member</p>
                                                    <small class="opacity-75">Engagement metric</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-dark text-white h-100">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-shield-exclamation fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo h($advanced_member_analytics['risk_analysis']['high_risk_members'] ?? 0); ?></h4>
                                                    <p class="mb-0 small">High Risk Members</p>
                                                    <small class="opacity-75"><?php echo h($advanced_member_analytics['risk_analysis']['medium_risk_members'] ?? 0); ?> medium risk</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-gradient text-white h-100" style="background: linear-gradient(45deg, #ff6b6b, #ee5a24);">
                                                <div class="card-body text-center">
                                                    <i class="bi bi-cash-stack fs-1 mb-2"></i>
                                                    <h4 class="mb-1"><?php echo h($advanced_member_analytics['risk_analysis']['high_fine_members'] ?? 0); ?></h4>
                                                    <p class="mb-0 small">High Fine Members</p>
                                                    <small class="opacity-75">Over $50 in fines</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Member Activity Levels Chart -->
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Member Activity Levels</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="activity-levels">
                                                        <?php if (!empty($advanced_member_analytics['activity_levels'])): ?>
                                                            <?php foreach ($advanced_member_analytics['activity_levels'] as $level): ?>
                                                                <?php
                                                                    $total_members = max(1, $stats['total_members'] ?? 1); // Prevent division by zero
                                                                    $member_count = $level['member_count'] ?? 0;
                                                                    $percentage = round(($member_count / $total_members) * 100, 1);
                                                                    $color_map = [
                                                                        'Inactive' => 'secondary',
                                                                        'Light Reader' => 'info',
                                                                        'Regular Reader' => 'primary',
                                                                        'Active Reader' => 'warning',
                                                                        'Power Reader' => 'success',
                                                                        'Active' => 'primary',
                                                                        'Returned' => 'success',
                                                                        'Overdue' => 'danger'
                                                                    ];
                                                                    $activity_level = $level['activity_level'] ?? 'Unknown';
                                                                    $color = $color_map[$activity_level] ?? 'secondary';
                                                                ?>
                                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                                    <span class="fw-medium"><?php echo h($activity_level); ?></span>
                                                                    <span class="badge bg-<?php echo $color; ?>"><?php echo h($member_count); ?> (<?php echo $percentage; ?>%)</span>
                                                                </div>
                                                                <div class="progress mb-3" style="height: 8px;">
                                                                    <div class="progress-bar bg-<?php echo $color; ?>" style="width: <?php echo $percentage; ?>%"></div>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        <?php else: ?>
                                                            <div class="text-center text-muted py-3">
                                                                <i class="bi bi-info-circle fs-1"></i>
                                                                <p class="mt-2">No activity data available</p>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Top Borrowers -->
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0"><i class="bi bi-trophy me-2"></i>Top 5 Borrowers</h6>
                                                    <a href="<?php echo url('members/statistics.php'); ?>" class="btn btn-sm btn-outline-primary">View All</a>
                                                </div>
                                                <div class="card-body">
                                                    <div class="list-group list-group-flush">
                                                        <?php foreach ($advanced_member_analytics['top_borrowers'] as $index => $borrower): ?>
                                                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                                                <div class="d-flex align-items-center">
                                                                    <span class="badge bg-primary rounded-pill me-2"><?php echo $index + 1; ?></span>
                                                                    <div>
                                                                        <div class="fw-medium"><?php echo h($borrower['first_name'] . ' ' . $borrower['last_name']); ?></div>
                                                                        <small class="text-muted"><?php echo h($borrower['email']); ?></small>
                                                                    </div>
                                                                </div>
                                                                <div class="text-end">
                                                                    <div class="fw-bold text-primary"><?php echo h($borrower['total_loans']); ?> loans</div>
                                                                    <small class="text-muted">
                                                                        <?php echo h($borrower['active_loans']); ?> active,
                                                                        <?php echo h($borrower['overdue_loans']); ?> overdue
                                                                    </small>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Recent Member Activity -->
                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="card bg-light">
                                                <div class="card-header d-flex justify-content-between align-items-center">
                                                    <h6 class="mb-0"><i class="bi bi-clock-history me-2"></i>Recent Member Activity (Last 7 Days)</h6>
                                                    <a href="<?php echo url('loans/index.php'); ?>" class="btn btn-sm btn-outline-primary">View All Loans</a>
                                                </div>
                                                <div class="card-body">
                                                    <?php if (!empty($advanced_member_analytics['recent_activity'])): ?>
                                                        <div class="table-responsive">
                                                            <table class="table table-sm table-hover">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Member</th>
                                                                        <th>Book</th>
                                                                        <th>Issue Date</th>
                                                                        <th>Due Date</th>
                                                                        <th>Status</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php foreach ($advanced_member_analytics['recent_activity'] as $activity): ?>
                                                                        <tr>
                                                                            <td><?php echo h($activity['first_name'] . ' ' . $activity['last_name']); ?></td>
                                                                            <td><?php echo h($activity['title']); ?></td>
                                                                            <td><?php echo formatDate($activity['issue_date']); ?></td>
                                                                            <td><?php echo formatDate($activity['due_date']); ?></td>
                                                                            <td>
                                                                                <?php if ($activity['status'] === 'borrowed'): ?>
                                                                                    <span class="badge bg-primary">Active</span>
                                                                                <?php elseif ($activity['status'] === 'returned'): ?>
                                                                                    <span class="badge bg-success">Returned</span>
                                                                                <?php else: ?>
                                                                                    <span class="badge bg-danger">Overdue</span>
                                                                                <?php endif; ?>
                                                                            </td>
                                                                        </tr>
                                                                    <?php endforeach; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    <?php else: ?>
                                                        <div class="text-center text-muted py-3">
                                                            <i class="bi bi-inbox fs-1"></i>
                                                            <p class="mt-2">No recent activity in the last 7 days</p>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Quick Summary -->
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h6 class="text-muted mb-3">Quick Summary</h6>
                                            <div class="row text-center">
                                                <div class="col-md-2-4">
                                                    <div class="border rounded p-3">
                                                        <h5 class="text-primary mb-1"><?php echo h($stats['total_members']); ?></h5>
                                                        <small class="text-muted">Total Members</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-2-4">
                                                    <div class="border rounded p-3">
                                                        <h5 class="text-success mb-1"><?php echo h($member_stats['members_who_borrowed']); ?></h5>
                                                        <small class="text-muted">Ever Borrowed</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-2-4">
                                                    <div class="border rounded p-3">
                                                        <h5 class="text-warning mb-1"><?php echo h($stats['active_loans']); ?></h5>
                                                        <small class="text-muted">Active Loans</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-2-4">
                                                    <div class="border rounded p-3">
                                                        <h5 class="text-danger mb-1"><?php echo h($stats['overdue_books']); ?></h5>
                                                        <small class="text-muted">Overdue Books</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-2-4">
                                                    <div class="border rounded p-3">
                                                        <h5 class="text-info mb-1">$<?php echo number_format($stats['total_fines'], 2); ?></h5>
                                                        <small class="text-muted">Total Fines</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Recent Books -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Recently Added Books</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Cover</th>
                                                <th>Title</th>
                                                <th>Author</th>
                                                <th>Available</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_books as $book): ?>
                                            <tr>
                                                <td class="text-center" style="width: 60px;">
                                                    <?php if (!empty($book['cover_image'])): ?>
                                                        <?php
                                                        // Check if the cover_image is a URL or a local file
                                                        $image_src = (strpos($book['cover_image'], 'http') === 0)
                                                            ? $book['cover_image']
                                                            : url('uploads/covers/' . $book['cover_image']);
                                                        ?>
                                                        <a href="#" class="cover-preview" data-bs-toggle="modal" data-bs-target="#coverModal"
                                                           data-img-src="<?php echo h($image_src); ?>"
                                                           data-title="<?php echo h($book['title']); ?>">
                                                            <img src="<?php echo h($image_src); ?>" alt="<?php echo h($book['title']); ?>"
                                                                 class="img-thumbnail" style="width: 40px; height: 60px; object-fit: cover;">
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary"><i class="bi bi-book"></i></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="../books/view.php?id=<?php echo h($book['id']); ?>" class="text-decoration-none">
                                                        <?php echo h($book['title']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo h($book['author']); ?></td>
                                                <td><?php echo h($book['available_quantity']); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-end">
                                <a href="../books/index.php" class="btn btn-sm btn-primary">View All Books</a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Loans -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Recent Loans</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Book</th>
                                                <th>Member</th>
                                                <th>Due Date</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_loans as $loan): ?>
                                            <tr>
                                                <td><?php echo h($loan['book_title']); ?></td>
                                                <td><?php echo h($loan['first_name'] . ' ' . $loan['last_name']); ?></td>
                                                <td><?php echo formatDate($loan['due_date']); ?></td>
                                                <td>
                                                    <?php if ($loan['status'] === 'borrowed'): ?>
                                                        <span class="badge bg-primary">Borrowed</span>
                                                    <?php elseif ($loan['status'] === 'returned'): ?>
                                                        <span class="badge bg-success">Returned</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Overdue</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer text-end">
                                <a href="../loans/index.php" class="btn btn-sm btn-primary">View All Loans</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Quick Actions</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#quickActionsCollapse" aria-expanded="true" aria-controls="quickActionsCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="quickActionsCollapse">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../books/add.php" class="btn btn-primary">
                                                    <i class="bi bi-plus-circle me-2"></i> Add New Book
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../members/add.php" class="btn btn-success">
                                                    <i class="bi bi-person-plus me-2"></i> Register Member
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../loans/issue.php" class="btn btn-info text-white">
                                                    <i class="bi bi-journal-arrow-up me-2"></i> Issue Book
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../loans/return.php" class="btn btn-warning text-white">
                                                    <i class="bi bi-journal-arrow-down me-2"></i> Return Book
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <hr class="my-3">

                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../reports/overdue.php" class="btn btn-danger">
                                                    <i class="bi bi-exclamation-triangle me-2"></i> Overdue Books
                                                    <?php if ($stats['overdue_books'] > 0): ?>
                                                        <span class="badge bg-white text-danger ms-1"><?php echo $stats['overdue_books']; ?></span>
                                                    <?php endif; ?>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="../reports/index.php" class="btn btn-secondary">
                                                    <i class="bi bi-file-earmark-bar-graph me-2"></i> Generate Reports
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <button class="btn btn-dark" onclick="showQuickSearch()">
                                                    <i class="bi bi-search me-2"></i> Quick Search
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="d-grid">
                                                <a href="settings.php" class="btn btn-outline-secondary">
                                                    <i class="bi bi-gear me-2"></i> System Settings
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if ($stats['overdue_books'] > 100): ?>
                                    <div class="row g-3 mt-2">
                                        <div class="col-md-12">
                                            <div class="alert alert-warning d-flex align-items-center" role="alert">
                                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                                <div class="flex-grow-1">
                                                    <strong>High Overdue Count Detected!</strong> You have <?php echo $stats['overdue_books']; ?> overdue books.
                                                    Consider using the balance tool to return very old overdue loans and normalize your dashboard.
                                                </div>
                                                <a href="../balance_overdue_books.php" class="btn btn-warning btn-sm ms-3">
                                                    <i class="bi bi-tools me-1"></i> Balance Dashboard
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Visualization Section -->
                <div class="row">
                    <!-- Book Categories Chart -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Books by Category</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#categoriesChartCollapse" aria-expanded="true" aria-controls="categoriesChartCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="categoriesChartCollapse">
                                <div class="card-body">
                                    <div style="height: 300px;">
                                        <canvas id="categoriesChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loans Activity Chart -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Loans Activity (Last 6 Months)</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#loansChartCollapse" aria-expanded="true" aria-controls="loansChartCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="loansChartCollapse">
                                <div class="card-body">
                                    <div style="height: 300px;">
                                        <canvas id="loansChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Health and Activity Section -->
                <div class="row">
                    <!-- System Health Dashboard -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">System Health</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#systemHealthCollapse" aria-expanded="true" aria-controls="systemHealthCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="systemHealthCollapse">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body p-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="mb-0">Database Size</h6>
                                                            <p class="mb-0 text-muted"><?php echo formatFileSize($db_size); ?></p>
                                                        </div>
                                                        <i class="bi bi-database fs-3 text-primary"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body p-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="mb-0">Disk Space</h6>
                                                            <p class="mb-0 text-muted"><?php echo $disk_free; ?> free of <?php echo $disk_total; ?></p>
                                                        </div>
                                                        <i class="bi bi-hdd fs-3 text-success"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body p-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="mb-0">PHP Version</h6>
                                                            <p class="mb-0 text-muted"><?php echo phpversion(); ?></p>
                                                        </div>
                                                        <i class="bi bi-filetype-php fs-3 text-info"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body p-3">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h6 class="mb-0">Last Backup</h6>
                                                            <p class="mb-0 text-muted"><?php echo $last_backup ? formatDate($last_backup) : 'Never'; ?></p>
                                                        </div>
                                                        <i class="bi bi-cloud-arrow-up fs-3 text-warning"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer text-end">
                                    <a href="backup.php" class="btn btn-sm btn-primary">Create Backup</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Activity Log -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Recent Activity</h5>
                                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#activityCollapse" aria-expanded="true" aria-controls="activityCollapse">
                                    <i class="bi bi-chevron-down"></i>
                                </button>
                            </div>
                            <div class="collapse show" id="activityCollapse">
                                <div class="card-body p-0">
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($activities as $activity): ?>
                                            <div class="list-group-item py-3">
                                                <div class="d-flex">
                                                    <div class="me-3">
                                                        <?php if ($activity['action'] === 'login'): ?>
                                                            <i class="bi bi-box-arrow-in-right text-success fs-4"></i>
                                                        <?php elseif ($activity['action'] === 'add'): ?>
                                                            <i class="bi bi-plus-circle text-primary fs-4"></i>
                                                        <?php elseif ($activity['action'] === 'edit'): ?>
                                                            <i class="bi bi-pencil text-warning fs-4"></i>
                                                        <?php elseif ($activity['action'] === 'delete'): ?>
                                                            <i class="bi bi-trash text-danger fs-4"></i>
                                                        <?php else: ?>
                                                            <i class="bi bi-activity text-info fs-4"></i>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <p class="mb-0"><?php echo h($activity['description']); ?></p>
                                                        <small class="text-muted">
                                                            <?php echo h($activity['username'] ?? 'System'); ?> •
                                                            <?php echo timeAgo($activity['timestamp']); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <div class="card-footer text-end">
                                    <a href="activity_log.php" class="btn btn-sm btn-secondary">View All Activity</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="<?php echo url('assets/js/dark-mode.js'); ?>"></script>

    <!-- Book Cover Modal -->
    <div class="modal fade" id="coverModal" tabindex="-1" aria-labelledby="coverModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="coverModalLabel">Book Cover</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalCoverImage" src="" class="img-fluid" alt="Book Cover">
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Function to show book cover in modal
            const coverLinks = document.querySelectorAll('.cover-preview');
            coverLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const imgSrc = this.getAttribute('data-img-src');
                    const title = this.getAttribute('data-title');
                    document.getElementById('modalCoverImage').src = imgSrc;
                    document.getElementById('coverModalLabel').textContent = title;
                });
            });

            // Book Categories Chart
            const ctxCategories = document.getElementById('categoriesChart');
            if (ctxCategories) {
                // Generate colors based on the number of categories
                const generateColors = (count) => {
                    const baseColors = [
                        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796',
                        '#5a5c69', '#e83e8c', '#fd7e14', '#20c997', '#6f42c1', '#17a2b8'
                    ];

                    const colors = [];
                    const hoverColors = [];

                    for (let i = 0; i < count; i++) {
                        const colorIndex = i % baseColors.length;
                        colors.push(baseColors[colorIndex]);

                        // Create a slightly darker version for hover
                        const color = baseColors[colorIndex];
                        const r = parseInt(color.slice(1, 3), 16) - 30;
                        const g = parseInt(color.slice(3, 5), 16) - 30;
                        const b = parseInt(color.slice(5, 7), 16) - 30;

                        hoverColors.push(`#${Math.max(0, r).toString(16).padStart(2, '0')}${Math.max(0, g).toString(16).padStart(2, '0')}${Math.max(0, b).toString(16).padStart(2, '0')}`);
                    }

                    return { colors, hoverColors };
                };

                const { colors, hoverColors } = generateColors(<?php echo count($category_labels); ?>);

                new Chart(ctxCategories, {
                    type: 'doughnut',
                    data: {
                        labels: <?php echo json_encode($category_labels); ?>,
                        datasets: [{
                            data: <?php echo json_encode($category_data); ?>,
                            backgroundColor: colors,
                            hoverBackgroundColor: hoverColors,
                            hoverBorderColor: "rgba(234, 236, 244, 1)",
                        }],
                    },
                    options: {
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 10,
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        cutout: '70%'
                    }
                });
            }

            // Loans Activity Chart
            const ctxLoans = document.getElementById('loansChart');
            if (ctxLoans) {
                new Chart(ctxLoans, {
                    type: 'line',
                    data: {
                        labels: <?php echo json_encode($loan_labels); ?>,
                        datasets: [{
                            label: 'Loans',
                            data: <?php echo json_encode($loan_counts); ?>,
                            backgroundColor: 'rgba(78, 115, 223, 0.05)',
                            borderColor: 'rgba(78, 115, 223, 1)',
                            pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                            fill: true
                        }],
                    },
                    options: {
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0 // Only show whole numbers
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.dataset.label}: ${context.raw} loans`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>

    <!-- Quick Search Modal -->
    <div class="modal fade" id="quickSearchModal" tabindex="-1" aria-labelledby="quickSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickSearchModalLabel">
                        <i class="bi bi-search me-2"></i>Quick Search
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-book me-2"></i>Search Books</h6>
                                </div>
                                <div class="card-body">
                                    <form id="bookSearchForm">
                                        <div class="mb-3">
                                            <input type="text" class="form-control" id="bookSearchInput" placeholder="Enter book title, author, or ISBN...">
                                        </div>
                                        <button type="submit" class="btn btn-primary btn-sm">
                                            <i class="bi bi-search me-1"></i>Search Books
                                        </button>
                                    </form>
                                    <div id="bookSearchResults" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="bi bi-people me-2"></i>Search Members</h6>
                                </div>
                                <div class="card-body">
                                    <form id="memberSearchForm">
                                        <div class="mb-3">
                                            <input type="text" class="form-control" id="memberSearchInput" placeholder="Enter member name or email...">
                                        </div>
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="bi bi-search me-1"></i>Search Members
                                        </button>
                                    </form>
                                    <div id="memberSearchResults" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions JavaScript -->
    <script>
        // Quick Search Functions
        function showQuickSearch() {
            const modal = new bootstrap.Modal(document.getElementById('quickSearchModal'));
            modal.show();
        }

        // Book Search
        document.getElementById('bookSearchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const query = document.getElementById('bookSearchInput').value.trim();
            if (query.length < 2) {
                document.getElementById('bookSearchResults').innerHTML = '<div class="alert alert-warning">Please enter at least 2 characters</div>';
                return;
            }

            document.getElementById('bookSearchResults').innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> Searching...</div>';

            fetch('../api/search_books.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({query: query})
            })
            .then(response => response.json())
            .then(data => {
                let html = '';
                if (data.success && data.books.length > 0) {
                    html = '<div class="list-group">';
                    data.books.forEach(book => {
                        html += `
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${book.title}</h6>
                                        <p class="mb-1 text-muted small">by ${book.author}</p>
                                        <small>Available: ${book.available_quantity}/${book.quantity}</small>
                                    </div>
                                    <div>
                                        <a href="../books/view.php?id=${book.id}" class="btn btn-sm btn-outline-primary">View</a>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                } else {
                    html = '<div class="alert alert-info">No books found matching your search.</div>';
                }
                document.getElementById('bookSearchResults').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('bookSearchResults').innerHTML = '<div class="alert alert-danger">Error searching books. Please try again.</div>';
            });
        });

        // Member Search
        document.getElementById('memberSearchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const query = document.getElementById('memberSearchInput').value.trim();
            if (query.length < 2) {
                document.getElementById('memberSearchResults').innerHTML = '<div class="alert alert-warning">Please enter at least 2 characters</div>';
                return;
            }

            document.getElementById('memberSearchResults').innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> Searching...</div>';

            fetch('../api/search_members.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({query: query})
            })
            .then(response => response.json())
            .then(data => {
                let html = '';
                if (data.success && data.members.length > 0) {
                    html = '<div class="list-group">';
                    data.members.forEach(member => {
                        html += `
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">${member.first_name} ${member.last_name}</h6>
                                        <p class="mb-1 text-muted small">${member.email}</p>
                                        <small>Member since: ${member.membership_date}</small>
                                    </div>
                                    <div>
                                        <a href="../members/view.php?id=${member.id}" class="btn btn-sm btn-outline-success">View</a>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                } else {
                    html = '<div class="alert alert-info">No members found matching your search.</div>';
                }
                document.getElementById('memberSearchResults').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('memberSearchResults').innerHTML = '<div class="alert alert-danger">Error searching members. Please try again.</div>';
            });
        });

        // Refresh Member Statistics Function
        function refreshMemberStats() {
            const refreshBtn = document.querySelector('[onclick="refreshMemberStats()"]');
            const originalIcon = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
            refreshBtn.disabled = true;

            fetch('dashboard_stats.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update main stats cards
                    document.querySelector('[data-stat="total_books"]').textContent = data.stats.total_books;
                    document.querySelector('[data-stat="available_books"]').textContent = data.stats.available_books;
                    document.querySelector('[data-stat="total_members"]').textContent = data.stats.total_members;
                    document.querySelector('[data-stat="active_loans"]').textContent = data.stats.active_loans;
                    document.querySelector('[data-stat="overdue_books"]').textContent = data.stats.overdue_books;

                    // Show success message
                    showNotification('Member statistics refreshed successfully!', 'success');

                    // Optionally reload the page to get fresh member analytics
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification('Failed to refresh statistics', 'error');
                }
            })
            .catch(error => {
                console.log('Stats refresh failed:', error);
                showNotification('Error refreshing statistics', 'error');
            })
            .finally(() => {
                // Restore button state
                refreshBtn.innerHTML = originalIcon;
                refreshBtn.disabled = false;
            });
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // Auto-refresh dashboard stats every 30 seconds
        setInterval(function() {
            fetch('dashboard_stats.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update stats cards
                    document.querySelector('[data-stat="total_books"]').textContent = data.stats.total_books;
                    document.querySelector('[data-stat="available_books"]').textContent = data.stats.available_books;
                    document.querySelector('[data-stat="total_members"]').textContent = data.stats.total_members;
                    document.querySelector('[data-stat="active_loans"]').textContent = data.stats.active_loans;
                    document.querySelector('[data-stat="overdue_books"]').textContent = data.stats.overdue_books;
                }
            })
            .catch(error => console.log('Stats refresh failed:', error));
        }, 30000);

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+K for quick search
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                showQuickSearch();
            }
        });

        // Add tooltips to buttons
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Ensure View Details links work properly
            document.querySelectorAll('.card-footer a').forEach(function(link) {
                // Add visual feedback
                link.style.cursor = 'pointer';
                link.style.pointerEvents = 'auto';
                link.style.zIndex = '1050';
                link.style.position = 'relative';

                // Remove any existing event listeners and add new ones
                link.addEventListener('click', function(e) {
                    // Don't prevent default - let the link work normally
                    console.log('View Details clicked:', this.href);

                    // Add visual feedback
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 100);

                    // Let the browser handle the navigation normally
                    return true;
                });

                // Add hover effect
                link.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                    this.style.borderRadius = '4px';
                    this.style.padding = '4px 8px';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                    this.style.padding = '0';
                });
            });

            // Make entire stats cards clickable as backup
            document.querySelectorAll('.stats-card').forEach(function(card) {
                const link = card.querySelector('.card-footer a');
                if (link && link.href && link.href !== '#') {
                    card.style.cursor = 'pointer';
                    card.addEventListener('click', function(e) {
                        // Only trigger if not clicking on the footer link directly
                        if (!e.target.closest('.card-footer a')) {
                            console.log('Card clicked, navigating to:', link.href);
                            window.location.href = link.href;
                        }
                    });
                }
            });

            // Fix for sign out button and UI issues
            const signOutBtn = document.querySelector('a[href="../logout.php"]');
            if (signOutBtn) {
                signOutBtn.style.zIndex = '1060';
                signOutBtn.style.position = 'relative';
                signOutBtn.style.pointerEvents = 'auto';

                // Add click handler to ensure it works
                signOutBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    // Force navigation
                    window.location.href = '../logout.php';
                });
            }

            // Hide notifications panel by default
            const notificationsContainer = document.getElementById('staticNotifications');
            if (notificationsContainer) {
                notificationsContainer.style.display = 'none';
            }

            // Handle close notifications button
            const closeNotificationsBtn = document.getElementById('closeNotifications');
            if (closeNotificationsBtn && notificationsContainer) {
                closeNotificationsBtn.addEventListener('click', function() {
                    notificationsContainer.style.display = 'none';
                });
            }

            // Auto-dismiss error alerts after 5 seconds
            const errorAlerts = document.querySelectorAll('.alert-danger');
            errorAlerts.forEach(function(alert) {
                setTimeout(function() {
                    if (alert.parentNode) {
                        alert.style.opacity = '0';
                        setTimeout(function() {
                            alert.remove();
                        }, 300);
                    }
                }, 5000);
            });
        });

        // Simple notification toggle function (backup)
        function toggleNotifications(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const container = document.getElementById('staticNotifications');
            if (container) {
                const isVisible = container.style.display === 'block';
                container.style.display = isVisible ? 'none' : 'block';
                console.log('Notifications toggled:', !isVisible ? 'shown' : 'hidden');
            }
        }
    </script>

    <!-- Enhanced Analytics Script -->
    <script src="js/enhanced_analytics.js"></script>

    <!-- Notifications Script -->
    <script src="<?php echo url('assets/js/notifications.js'); ?>"></script>

    <!-- Fix for View Details Links -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Fixing View Details links...');

            // Ensure all view-details-link elements are clickable
            const viewDetailsLinks = document.querySelectorAll('.view-details-link');
            const cardFooterLinks = document.querySelectorAll('.card-footer a');
            const allLinks = [...viewDetailsLinks, ...cardFooterLinks];

            allLinks.forEach(function(link, index) {
                // Force clickable properties
                link.style.pointerEvents = 'auto !important';
                link.style.cursor = 'pointer !important';
                link.style.zIndex = '1050';
                link.style.position = 'relative';
                link.style.display = 'inline-block';

                // Validate the href
                if (!link.href || link.href === '#' || link.href.includes('undefined')) {
                    console.error('Invalid link href detected:', link.href, link);
                    return;
                }

                // Remove any existing click handlers
                link.onclick = null;

                // Add click event listener
                link.addEventListener('click', function(e) {
                    console.log('View Details link clicked:', this.href);

                    // Validate href before navigation
                    if (this.href && this.href !== '#' && !this.href.includes('undefined')) {
                        // Add visual feedback
                        this.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 150);

                        // Allow normal navigation
                        return true;
                    } else {
                        e.preventDefault();
                        console.error('Invalid or empty href, preventing navigation:', this.href);
                        alert('Error: Invalid link. Please contact administrator.');
                        return false;
                    }
                });

                // Add visual feedback on hover
                link.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.8';
                    this.style.textDecoration = 'underline';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.opacity = '1';
                    this.style.textDecoration = 'none';
                });
            });

            console.log('View Details links processed:', allLinks.length, 'total links');

            // Debug: Log all href values
            allLinks.forEach((link, i) => {
                console.log(`Link ${i + 1}:`, link.href, link.textContent.trim());
            });
        });
    </script>

    <!-- Notifications JavaScript -->
    <script src="../assets/js/notifications.js"></script>
    <script src="../assets/js/notifications-dropdown.js"></script>

    <!-- Notification Bell JavaScript - Must be at the end -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Setting up notification bell functionality...');

            // Handle notification bell click - exactly like librarian dashboard
            const notificationBell = document.getElementById('notificationBell');
            const staticNotificationsContainer = document.getElementById('staticNotifications');

            console.log('Bell element found:', !!notificationBell);
            console.log('Container element found:', !!staticNotificationsContainer);

            if (notificationBell && staticNotificationsContainer) {
                console.log('Adding click handler to notification bell');

                // Initialize container as hidden
                staticNotificationsContainer.style.display = 'none';

                // Function to toggle notifications
                function toggleNotifications() {
                    const isHidden = staticNotificationsContainer.style.display === 'none' ||
                                   staticNotificationsContainer.style.display === '';

                    if (isHidden) {
                        staticNotificationsContainer.style.display = 'block';
                        staticNotificationsContainer.style.opacity = '1';
                        staticNotificationsContainer.style.transform = 'translateY(0)';
                        staticNotificationsContainer.classList.add('show');
                        notificationBell.setAttribute('aria-expanded', 'true');
                        localStorage.setItem('notificationsHidden', 'false');
                        console.log('Notifications shown');
                    } else {
                        staticNotificationsContainer.classList.remove('show');
                        staticNotificationsContainer.style.opacity = '0';
                        staticNotificationsContainer.style.transform = 'translateY(-10px)';
                        notificationBell.setAttribute('aria-expanded', 'false');
                        setTimeout(() => {
                            staticNotificationsContainer.style.display = 'none';
                        }, 300);
                        localStorage.setItem('notificationsHidden', 'true');
                        console.log('Notifications hidden');
                    }
                }

                // Handle click events
                notificationBell.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Notification bell clicked!');
                    toggleNotifications();
                });

                // Handle keyboard events
                notificationBell.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Notification bell activated via keyboard!');
                        toggleNotifications();
                    }
                });

                // Handle close button click
                const closeNotificationsBtn = document.getElementById('closeNotifications');
                if (closeNotificationsBtn) {
                    closeNotificationsBtn.addEventListener('click', function() {
                        if (staticNotificationsContainer) {
                            staticNotificationsContainer.classList.remove('show');
                            setTimeout(() => {
                                staticNotificationsContainer.style.display = 'none';
                            }, 300);
                            localStorage.setItem('notificationsHidden', 'true');
                            console.log('Notifications closed via close button');
                        }
                    });
                }

                // Handle minimize button click
                const minimizeNotificationsBtn = document.getElementById('minimizeNotifications');
                if (minimizeNotificationsBtn) {
                    minimizeNotificationsBtn.addEventListener('click', function() {
                        if (staticNotificationsContainer) {
                            if (staticNotificationsContainer.classList.contains('minimized')) {
                                // Expand
                                staticNotificationsContainer.classList.remove('minimized');
                                this.innerHTML = '<i class="bi bi-dash"></i>';
                                this.title = 'Minimize';
                                localStorage.setItem('notificationsMinimized', 'false');
                            } else {
                                // Minimize
                                staticNotificationsContainer.classList.add('minimized');
                                this.innerHTML = '<i class="bi bi-arrows-angle-expand"></i>';
                                this.title = 'Expand';
                                localStorage.setItem('notificationsMinimized', 'true');
                            }
                        }
                    });
                }

                // Handle refresh button click with actual refresh functionality
                const refreshNotificationsBtn = document.getElementById('refreshNotifications');
                if (refreshNotificationsBtn) {
                    refreshNotificationsBtn.addEventListener('click', function() {
                        const originalIcon = this.innerHTML;
                        this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
                        this.disabled = true;

                        // Add spinning animation
                        const spinner = this.querySelector('i');
                        spinner.style.animation = 'spin 1s linear infinite';

                        // Simulate refresh (you can replace this with actual AJAX call)
                        setTimeout(() => {
                            this.innerHTML = originalIcon;
                            this.disabled = false;

                            // Show success feedback
                            const toast = document.createElement('div');
                            toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
                            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                            toast.innerHTML = `
                                <i class="bi bi-check-circle me-2"></i>Notifications refreshed successfully!
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            `;
                            document.body.appendChild(toast);

                            setTimeout(() => {
                                if (toast.parentNode) toast.remove();
                            }, 3000);
                        }, 1000);

                        console.log('Refresh button clicked');
                    });
                }

                // Handle mark as read button clicks
                const markReadButtons = document.querySelectorAll('.mark-read-btn');
                markReadButtons.forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const notificationId = this.getAttribute('data-id');
                        const notificationItem = this.closest('.notification-item');

                        // Mark as read in UI
                        notificationItem.classList.remove('unread');
                        this.style.display = 'none';

                        // Update notification count
                        const badge = document.querySelector('.notification-badge');
                        const counter = document.querySelector('.notification-counter');

                        if (badge && counter) {
                            let count = parseInt(counter.textContent) - 1;
                            counter.textContent = count;

                            if (count <= 0) {
                                badge.style.display = 'none';
                                counter.style.display = 'none';
                                const markAllRead = document.querySelector('.mark-all-read');
                                if (markAllRead) markAllRead.style.display = 'none';
                            } else {
                                badge.textContent = count > 9 ? '9+' : count;
                            }
                        }

                        // Send AJAX request to mark as read
                        if (notificationId) {
                            fetch('../notifications/mark_read.php', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/x-www-form-urlencoded',
                                },
                                body: 'notification_id=' + notificationId
                            })
                            .catch(error => {
                                console.error('Error marking notification as read:', error);
                            });
                        }
                    });
                });

                // Click outside to close notifications
                document.addEventListener('click', function(e) {
                    if (staticNotificationsContainer && notificationBell) {
                        if (!staticNotificationsContainer.contains(e.target) && !notificationBell.contains(e.target)) {
                            if (staticNotificationsContainer.style.display === 'block') {
                                staticNotificationsContainer.classList.remove('show');
                                setTimeout(() => {
                                    staticNotificationsContainer.style.display = 'none';
                                }, 300);
                                localStorage.setItem('notificationsHidden', 'true');
                                console.log('Notifications closed by clicking outside');
                            }
                        }
                    }
                });

                console.log('Notification bell setup complete!');
            } else {
                console.error('Could not find notification bell or container elements');
                console.log('Available elements:', {
                    bell: document.getElementById('notificationBell'),
                    container: document.getElementById('staticNotifications')
                });
            }
        });
    </script>
</body>
</html>
